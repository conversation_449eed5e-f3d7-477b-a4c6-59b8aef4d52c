import React, { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const FAQ = () => {
  const { t } = useLanguage();
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section id="faq" className="mb-16">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">{t('faq.title')}</h2>
      <div className="bg-white rounded-xl shadow-md overflow-hidden divide-y divide-gray-200">
        {/* {t('faq.questions').map((item, index) => (
          <div key={index} className="p-6">
            <button onClick={() => toggleFAQ(index)} className="w-full flex justify-between items-center text-left">
              <h3 className="text-lg font-medium text-gray-800">{item.question}</h3>
              <i className={`fas fa-chevron-down text-blue-500 transition-transform ${openIndex === index ? 'rotate-180' : ''}`}></i>
            </button>
            {openIndex === index && <p className="mt-3 text-gray-600">{item.answer}</p>}
          </div>
        ))} */}

        {Array.isArray(t('faq.questions')) ? (
          t('faq.questions').map((item, index) => (
            <div key={index} className="p-6">
              <button onClick={() => toggleFAQ(index)} className="w-full flex justify-between items-center text-left">
                <h3 className="text-lg font-medium text-gray-800">{item.question}</h3>
                <i className={`fas fa-chevron-down text-blue-500 transition-transform ${openIndex === index ? 'rotate-180' : ''}`}></i>
              </button>
              {openIndex === index && <p className="mt-3 text-gray-600">{item.answer}</p>}
            </div>
          ))
        ) : (
          <p className="p-6 text-red-500">FAQ data is not available or misconfigured.</p>
        )}

      </div>
    </section>
  );
};

export default FAQ;