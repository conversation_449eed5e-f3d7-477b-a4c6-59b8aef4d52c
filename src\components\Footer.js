import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const Footer = () => {
  const { t } = useLanguage();

  return (
    <footer className="bg-[#008000] text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-bold mb-4">{t('header.title')}</h3>
            <p className="text-gray-400">{t('footer.description')}</p>
          </div>
          <div>
            <h4 className="text-md font-semibold mb-4">{t('footer.quickLinks')}</h4>
            <ul className="space-y-2">
              <li><a href="#report" className="text-gray-400 hover:text-white transition">{t('header.nav.report')}</a></li>
              <li><a href="#status" className="text-gray-400 hover:text-white transition">{t('header.nav.status')}</a></li>
              <li><a href="#faq" className="text-gray-400 hover:text-white transition">{t('header.nav.faq')}</a></li>
            </ul>
          </div>
          <div>
            <h4 className="text-md font-semibold mb-4">{t('footer.contact')}</h4>
            <ul className="space-y-2 text-gray-400">
              <li className="flex items-center"><i className="fas fa-phone-alt mr-2"></i><span>{t('footer.contactInfo.phone')}</span></li>
              <li className="flex items-center"><i className="fas fa-envelope mr-2"></i><span>{t('footer.contactInfo.email')}</span></li>
              <li className="flex items-center"><i className="fas fa-map-marker-alt mr-2"></i><span>{t('footer.contactInfo.address')}</span></li>
            </ul>
          </div>
          <div>
            <h4 className="text-md font-semibold mb-4">{t('footer.followUs')}</h4>
            {/* <div className="flex space-x-4">
              <a href="https://www.facebook.com/EEUOfficia" className="text-gray-400 hover:text-white transition"><i className="fab fa-facebook-f text-xl"></i></a>
              <a href="https://twitter.com/EEUOfficia" className="text-gray-400 hover:text-white transition"><i className="fab fa-twitter text-xl"></i></a>
              <a href="https://www.facebook.com/EEUOfficia" className="text-gray-400 hover:text-white transition"><i className="fab fa-instagram text-xl"></i></a>
            </div> */}
            <div className="flex space-x-4">
  <a
    href="https://web.facebook.com/EEUOfficia"
    className="text-gray-400 hover:text-white transition"
    aria-label="Facebook"
    target="_blank"
    rel="noopener noreferrer"
  >
    <i className="fab fa-facebook-f text-xl"></i>
  </a>
  <a
    href="https://x.com/EEUOfficia"
    className="text-gray-400 hover:text-white transition"
    aria-label="Twitter"
    target="_blank"
    rel="noopener noreferrer"
  >
    <i className="fab fa-twitter text-xl"></i>
  </a>
  <a
    href="https://www.youtube.com/channel/UCV96qeHN0lvva6JcNJ0g-LQ"
    className="text-gray-400 hover:text-white transition"
    aria-label="YouTube"
    target="_blank"
    rel="noopener noreferrer"
  >
    <i className="fab fa-youtube text-xl"></i>
  </a>
  <a
    href="https://t.me/eeuethiopia"
    className="text-gray-400 hover:text-white transition"
    aria-label="Telegram"
    target="_blank"
    rel="noopener noreferrer"
  >
    <i className="fab fa-telegram-plane text-xl"></i>
  </a>
</div>
          </div>
        </div>
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>{t('footer.copyright')}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;