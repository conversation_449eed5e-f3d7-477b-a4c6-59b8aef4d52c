export default {
  // Header
  header: {
    title: "Ethiopian Electric Utility",
    subtitle: "Power Outage Reporting System",
    nav: {
      home: "Home",
      report: "Report Outage",
      status: "Check Status",
      faq: "FAQ",
      contact: "Contact"
    },
    auth: {
      login: "Login",
      signup: "Sign Up",
      logout: "Logout",
      welcome: "Welcome back, {{name}}!"
    }
  },

  // Hero Section
  hero: {
    title: "Report Power Outages Quickly & Easily",
    subtitle: "Help us restore power faster by reporting outages in your area. Our team responds promptly to all reports.",
    cta: "Report Outage Now",
    emergency: "Emergency? Call 905"
  },

  // Report Outage
  report: {
    title: "Report a Power Outage",
    quickMode: "Quick Report Mode",
    quickModeDesc: "Your personal details are saved. Just fill in the outage address and details below.",
    fields: {
      name: "Your Name",
      nameRequired: "Name is required",
      nameMax: "Name must be 50 characters or less",
      phone: "Phone Number",
      phoneRequired: "Phone number is required",
      phoneInvalid: "Please enter a valid phone number",
      caNumber: "CA Number or BP Number",
      caNumberRequired: "CA or BP number is required",
      caNumberInvalid: "Must be exactly 10 digits (BP Number) or 12 digits (CA Number)",
      address: "Address",
      addressRequired: "Address is required",
      complaint: "Complaint",
      complaintRequired: "Please select a complaint type",
      content: "Additional Details",
      contentPlaceholder: "Describe what happened, any visible damage, time of outage, etc."
    },
    placeholders: {
      name: "Enter your name",
      phone: "912345678",
      caNumber: "BP: 10 digits, CA: 12 digits",
      address: "Enter the address where outage occurred (max 150 characters)",
      complaint: "Select a complaint type"
    },
    complaintTypes: {
      noSupplyOne: "No Supply one house",
      noSupplyPartial: "No Supply partially",
      wirecut: "Wirecut",
      voltageDrop: "Voltage Drop",
      emergency: "Emergency"
    },
    submit: "Submit Outage Report",
    beforeReport: "Before You Report",
    checklist: [
      "Check if your neighbors also have power",
      "Verify your circuit breakers haven't tripped",
      "Look for any visible damage to power lines",
      "Check the \"Check Status\" section below to see if people in your area already have a reported outage for today."
    ],
    success: {
      title: "Report Submitted Successfully!",
      message: "Thank you for your report. We have received your outage information and will investigate promptly.",
      savedInfo: "Your personal details are saved for future reports!",
      close: "Close"
    },
    errors: {
      submitFailed: "Submission Failed",
      submitFailedMessage: "Failed to submit outage report. Please try again.",
      networkError: "Network Error",
      networkErrorMessage: "Error submitting outage report. Please check your connection and try again."
    }
  },

  // Check Status
  status: {
    title: "Check Status",
    subtitle: "Reports & Complaints",
    search: "Search messages...",
    searchPlaceholder: "Search messages...",
    filters: {
      allStatuses: "All Statuses",
      refresh: "Refresh"
    },
    statistics: {
      total: "Total Messages",
      unread: "Unread",
      read: "Read",
      starred: "Starred"
    },
    messageDetails: {
      from: "From",
      address: "Address",
      caNumber: "CA Number",
      requestNumber: "Request #",
      remark: "Remark",
      created: "Created",
      updated: "Updated",
      id: "ID"
    },
    statuses: {
      received: "Received",
      investigating: "Investigating",
      in_progress: "In Progress",
      resolved: "Resolved",
      closed: "Closed",
      pending: "Pending",
      repairing: "Repairing",
      restored: "Restored"

    },
    pagination: {
      showing: "Showing page {{current}} of {{total}} ({{count}} total messages)",
      displaying: "Displaying {{count}} messages",
      previous: "Previous",
      next: "Next"
    },
    noMessages: "No messages found",
    noMessagesDesc: "Try adjusting your search or filters",
    loading: "Loading messages..."
  },

  // FAQ
  faq: {
    title: "Frequently Asked Questions",
    questions: [
      {
        question: "How quickly will my outage be restored?",
        answer: "Most outages are restored within 2-4 hours. Emergency situations receive immediate attention. We'll keep you updated on progress."
      },
      {
        question: "Can I track the status of my report?",
        answer: "Yes! Use the 'Check Status' section above to search for updates on your area's outage status using your address or CA number."
      },
      {
        question: "What information do I need to report an outage?",
        answer: "You'll need your name, phone number, CA/BP number, and the address where the outage occurred. Additional details about the situation are helpful."
      },
      {
        question: "Should I report if my neighbors already reported?",
        answer: "Check the status section first to see if your area is already reported for today. if reported, there's no need to report multiple times."
      },
      {
        question: "What's the difference between CA and BP numbers?",
        answer: "CA numbers are 12 digits for regular customers account number. BP numbers are 10 digits for business partners number. Both can be found on your electricity bill."
      }
    ]
  },

  // Emergency Banner
  emergency: {
    title: "Emergency Outage Alert",
    message: "If you're experiencing a power emergency or see downed power lines, call our emergency hotline immediately.",
    phone: "Emergency: 905",
    cta: "Call Now"
  },

  // Footer
  footer: {
    description: "Ethiopian Electric Utility is committed to providing reliable power service to all communities across Ethiopia.",
    quickLinks: "Quick Links",
    contact: "Contact Info",
    followUs: "Follow Us",
    links: {
      about: "About EEU",
      services: "Our Services",
      news: "News & Updates",
      careers: "Careers",
      privacy: "Privacy Policy",
      terms: "Terms of Service"
    },
    contactInfo: {
      address: "Addis Ababa, Ethiopia",
      phone: "905",
      email: "<EMAIL>"
    },
    copyright: "© 2024 Ethiopian Electric Utility. All rights reserved."
  },

  // Login
  login: {
    title: "Login to Your Account",
    email: "Email Address",
    password: "Password",
    remember: "Remember me",
    forgot: "Forgot Password?",
    submit: "Sign In",
    noAccount: "Don't have an account?",
    signup: "Sign up here",
    errors: {
      email: "Please enter a valid email address",
      password: "Password is required",
      invalid: "Invalid email or password"
    }
  },

  // Signup
  signup: {
    title: "Create Your Account",
    name: "Full Name",
    email: "Email Address",
    phone: "Phone Number",
    password: "Password",
    confirmPassword: "Confirm Password",
    submit: "Create Account",
    hasAccount: "Already have an account?",
    login: "Sign in here",
    errors: {
      name: "Name is required",
      email: "Please enter a valid email address",
      phone: "Please enter a valid phone number",
      password: "Password must be at least 6 characters",
      confirmPassword: "Passwords do not match"
    }
  },

  // Common
  common: {
    loading: "Loading...",
    error: "Error",
    success: "Success",
    cancel: "Cancel",
    save: "Save",
    edit: "Edit",
    delete: "Delete",
    confirm: "Confirm",
    yes: "Yes",
    no: "No",
    ok: "OK",
    close: "Close",
    back: "Back",
    next: "Next",
    previous: "Previous",
    required: "Required",
    optional: "Optional",
    saved: "saved"
  }
};
